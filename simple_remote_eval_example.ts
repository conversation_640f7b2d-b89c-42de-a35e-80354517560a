/**
 * Simple Remote Eval Example (TypeScript)
 * 
 * This example demonstrates how to create a remote evaluation that can be run
 * from the Braintrust Playground. It shows:
 * 
 * 1. Basic task implementation
 * 2. Configurable parameters using Zod schemas
 * 3. Custom scoring
 * 4. How to run the remote eval server
 * 
 * To run this example:
 * 1. Save this file as simple_remote_eval_example.ts
 * 2. Run: npx braintrust eval simple_remote_eval_example.ts --dev
 * 3. The server will start at http://localhost:8300
 * 4. Add this URL to your project's remote eval sources in Braintrust
 * 5. Use it from the playground!
 */

import { Eval, initDataset, wrapOpenAI, currentSpan } from "braintrust";
import { Levenshtein } from "autoevals";
import OpenAI from "openai";
import { z } from "zod";

// Initialize OpenAI client (wrapped for Braintrust logging)
const client = wrapOpenAI(new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY 
}));

// Define parameter schemas using Zod
const temperatureParam = z
  .number()
  .min(0)
  .max(2)
  .default(0.7)
  .describe("Temperature for the LLM response (0-2)");

const maxTokensParam = z
  .number()
  .min(1)
  .max(4000)
  .default(150)
  .describe("Maximum number of tokens to generate");

const systemMessageParam = z
  .string()
  .default("You are a helpful assistant. Answer questions clearly and concisely.")
  .describe("System message for the LLM");

const includeExamplesParam = z
  .boolean()
  .default(false)
  .describe("Whether to include examples in the prompt");

// Define the main task function
async function simpleQATask(input: string, { parameters }: { parameters: any }) {
  const span = currentSpan();

  // Log the input and parameters
  span.log({
    input: input,
    metadata: {
      message: "Starting Q&A task",
      parameters: {
        temperature: parameters.temperature,
        max_tokens: parameters.max_tokens,
        include_examples: parameters.include_examples
      }
    }
  });

  // Build the prompt based on parameters
  let prompt = input;

  if (parameters.include_examples) {
    prompt = `Here are some examples:
Q: What is 2+2?
A: 4

Q: What is the capital of France?
A: Paris

Now answer this question:
Q: ${input}
A:`;

    span.log({
      metadata: {
        message: "Added examples to prompt",
        enhanced_prompt: prompt
      }
    });
  }

  try {
    const startTime = Date.now();

    const completion = await client.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: parameters.system_message },
        { role: "user", content: prompt }
      ],
      temperature: parameters.temperature,
      max_tokens: parameters.max_tokens,
    });

    const endTime = Date.now();
    const response = completion.choices[0]?.message?.content?.trim() || "No response";

    // Log the successful completion
    span.log({
      output: response,
      metrics: {
        response_time_ms: endTime - startTime,
        tokens_used: completion.usage?.total_tokens || 0,
      },
      metadata: {
        message: "LLM call completed successfully",
        model_used: "gpt-3.5-turbo",
        response_length: response.length
      }
    });

    return response;
  } catch (error) {
    // Log the error
    span.log({
      error: error instanceof Error ? error.message : String(error),
      metadata: {
        message: "Error calling OpenAI",
        level: "error"
      }
    });

    console.error("Error calling OpenAI:", error);
    return "Error: Failed to generate response";
  }
}

// Define a custom scorer
function customLengthScorer({ output }: { output: string }) {
  const span = currentSpan();
  const length = output.length;

  // Log scorer execution
  span.log({
    metadata: {
      message: "Running length appropriateness scorer",
      output_length: length
    }
  });

  // Score based on response length (prefer responses between 10-200 characters)
  let score = 1.0;
  let rationale = "Appropriate length";

  if (length < 10) {
    score = 0.3; // Too short
    rationale = "Response too short";
  } else if (length > 200) {
    score = 0.7; // Too long
    rationale = "Response too long";
  }

  // Log the scoring result
  span.log({
    metadata: {
      message: "Length scorer completed",
      score_assigned: score,
      scoring_rationale: rationale
    }
  });

  return {
    name: "length_appropriateness",
    score: score,
    metadata: {
      response_length: length,
      rationale: rationale
    }
  };
}

// Define the remote evaluation
Eval("Simple Q&A Remote Eval", {
  data: initDataset("local dev", { dataset: "qa_examples" }), // Dataset is ignored in remote evals
  task: simpleQATask,
  scores: [
    Levenshtein,           // Built-in similarity scorer
    customLengthScorer,    // Custom scorer
  ],
  parameters: {
    temperature: temperatureParam,
    max_tokens: maxTokensParam,
    system_message: systemMessageParam,
    include_examples: includeExamplesParam,
  },
  metadata: {
    description: "A simple Q&A evaluation that demonstrates remote eval capabilities",
    version: "1.0",
    author: "Braintrust User"
  }
});

/**
 * How to use this remote eval:
 *
 * 1. Start the dev server:
 *    npx braintrust eval simple_remote_eval_example.ts --dev
 *
 * 2. The server will start at http://localhost:8300
 *
 * 3. In Braintrust:
 *    - Go to your project's Configuration > Remote evals
 *    - Add "http://localhost:8300" as a remote eval source
 *    - Go to a playground and click "+ Remote" in the Task pane
 *    - Select "Simple Q&A Remote Eval"
 *
 * 4. Test with sample data like:
 *    [
 *      {"input": "What is the capital of France?", "expected": "Paris"},
 *      {"input": "Explain photosynthesis briefly", "expected": "Process where plants convert sunlight to energy"},
 *      {"input": "What is 2+2?", "expected": "4"}
 *    ]
 *
 * 5. Adjust the parameters in the playground:
 *    - temperature: Control randomness (0-2)
 *    - max_tokens: Limit response length
 *    - system_message: Customize the AI's behavior
 *    - include_examples: Add example Q&A pairs to the prompt
 *
 * The evaluation will run your task function with the provided data and score
 * the outputs using both the built-in Levenshtein distance scorer and the
 * custom length appropriateness scorer.
 *
 * LOGGING IN REMOTE EVALS:
 * This example demonstrates comprehensive logging using currentSpan().log():
 *
 * - Use `input`, `output`, `expected` for main data
 * - Use `metadata` for custom messages and debugging info
 * - Use `metrics` for numerical measurements (response times, token counts, etc.)
 * - Use `error` for error messages
 * - Logs appear in the Braintrust UI under each evaluation run
 * - Both task functions and custom scorers can generate logs
 * - Logs help debug issues and understand eval behavior
 */
